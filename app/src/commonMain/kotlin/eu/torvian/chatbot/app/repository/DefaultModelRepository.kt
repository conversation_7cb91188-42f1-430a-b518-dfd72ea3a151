package eu.torvian.chatbot.app.repository

import arrow.core.Either
import arrow.core.right
import eu.torvian.chatbot.app.domain.contracts.DataState
import eu.torvian.chatbot.app.service.api.ModelApi
import eu.torvian.chatbot.common.models.AddModelRequest
import eu.torvian.chatbot.common.models.ApiKeyStatusResponse
import eu.torvian.chatbot.common.models.LLMModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

/**
 * Default implementation of [ModelRepository] that manages LLM model configurations.
 *
 * This repository maintains an internal cache of model data using [MutableStateFlow] and
 * provides reactive updates to all observers. It delegates API operations to the injected
 * [ModelApi] and handles comprehensive error management through [RepositoryError].
 *
 * The repository ensures data consistency by automatically updating the internal StateFlow
 * whenever successful CRUD operations occur, eliminating the need for manual cache invalidation.
 *
 * @property modelApi The API client for model-related operations
 */
class DefaultModelRepository(
    private val modelApi: ModelApi
) : ModelRepository {

    private val _models = MutableStateFlow<DataState<RepositoryError, List<LLMModel>>>(DataState.Idle)
    override val models: StateFlow<DataState<RepositoryError, List<LLMModel>>> = _models.asStateFlow()

    override suspend fun loadModels(): Either<RepositoryError, Unit> {
        // Prevent duplicate loading operations
        if (_models.value.isLoading) return Unit.right()

        _models.update { DataState.Loading }

        return modelApi.getAllModels()
            .map { modelList ->
                _models.update { DataState.Success(modelList) }
            }
            .mapLeft { apiResourceError ->
                val repositoryError = apiResourceError.toRepositoryError("Failed to load models")
                _models.update { DataState.Error(repositoryError) }
                repositoryError
            }
    }

    override suspend fun addModel(request: AddModelRequest): Either<RepositoryError, LLMModel> {
        return modelApi.addModel(request)
            .map { newModel ->
                updateModelsState { it + newModel }
                newModel
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to add model")
            }
    }

    override suspend fun getModelById(modelId: Long): Either<RepositoryError, LLMModel> {
        return modelApi.getModelById(modelId)
            .map { model ->
                updateModelsState { list -> list.map { if (it.id == model.id) model else it } }
                model
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to get model by ID")
            }
    }

    override suspend fun updateModel(model: LLMModel): Either<RepositoryError, Unit> {
        return modelApi.updateModel(model)
            .map {
                updateModelsState { list -> list.map { if (it.id == model.id) model else it } }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to update model")
            }
    }

    override suspend fun deleteModel(modelId: Long): Either<RepositoryError, Unit> {
        return modelApi.deleteModel(modelId)
            .map {
                updateModelsState { list -> list.filter { it.id != modelId } }
            }
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to delete model")
            }
    }

    override suspend fun getModelApiKeyStatus(modelId: Long): Either<RepositoryError, ApiKeyStatusResponse> {
        return modelApi.getModelApiKeyStatus(modelId)
            .mapLeft { apiResourceError ->
                apiResourceError.toRepositoryError("Failed to get model API key status")
            }
    }

    /**
     * Updates the internal StateFlow of models using the provided transformation.
     *
     * @param transform A function that takes the current list of models and returns an updated list.
     */
    private fun updateModelsState(transform: (List<LLMModel>) -> List<LLMModel>) {
        _models.update { currentState ->
            when (currentState) {
                is DataState.Success -> DataState.Success(transform(currentState.data))
                else -> currentState
            }
        }
    }
}
