package eu.torvian.chatbot.app.viewmodel.chat.usecase

import eu.torvian.chatbot.app.repository.ChatRepository
import eu.torvian.chatbot.app.viewmodel.common.ErrorNotifier
import eu.torvian.chatbot.app.utils.misc.kmpLogger
import eu.torvian.chatbot.app.viewmodel.chat.state.SessionState

/**
 * Use case for deleting chat messages in the reactive architecture.
 *
 * This use case follows the action-only pattern where it:
 * 1. Deletes the message via repository
 * 2. Triggers a session reload to refresh the reactive state
 */
class DeleteMessageUseCase(
    private val chatRepository: ChatRepository,
    private val state: ChatState,
    private val loadSessionUseCase: LoadSessionUseCase,
    private val errorNotifier: ErrorNotifier
) {

    private val logger = kmpLogger<DeleteMessageUseCase>()

    /**
     * Deletes a specific message and its children from the session.
     * After successful deletion, reloads the session to update the reactive state.
     *
     * @param messageId The ID of the message to delete
     */
    suspend fun execute(messageId: Long) {
        val sessionId = state.activeSessionId.value ?: return

        logger.info("Deleting message $messageId from session $sessionId")

        chatRepository.deleteMessage(messageId)
            .fold(
                ifLeft = { repositoryError ->
                    logger.error("Delete message repository error: ${repositoryError.message}")
                    errorNotifier.repositoryError(
                        error = repositoryError,
                        shortMessage = "Failed to delete message"
                    )
                },
                ifRight = {
                    logger.info("Successfully deleted message $messageId")
                    // Repository automatically updates the message cache
                    // Reload the session to update the reactive state correctly.
                    loadSessionUseCase.execute(sessionId, forceReload = true)
                }
            )
    }
}
